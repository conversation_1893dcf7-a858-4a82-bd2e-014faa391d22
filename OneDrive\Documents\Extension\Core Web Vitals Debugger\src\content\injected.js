
(function() {
  'use strict';
  
  // Import web-vitals functions
  const { onLCP, onCLS, onINP } = webVitals;
  
  let performanceEntries = [];
  let observers = [];
  
  function getElementSelector(element) {
    if (!element) return null;
    
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      }
    }
    
    // Fallback to nth-child selector
    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children);
      const index = siblings.indexOf(element) + 1;
      return `${parent.tagName.toLowerCase()} > ${element.tagName.toLowerCase()}:nth-child(${index})`;
    }
    
    return element.tagName.toLowerCase();
  }
  
  function sendMetricUpdate(metricName, value, element = null) {
    const elementSelector = element ? getElementSelector(element) : null;
    
    window.postMessage({
      type: 'CWV_METRIC_UPDATE',
      metric: metricName,
      value: value,
      element: elementSelector,
      timestamp: performance.now()
    }, '*');
    
    // Store performance entry
    performanceEntries.push({
      metric: metricName,
      value: value,
      element: elementSelector,
      timestamp: performance.now(),
      url: location.href
    });
  }
  
  function startCollection() {
    // Clear previous observers
    observers.forEach(observer => {
      if (observer.disconnect) observer.disconnect();
    });
    observers = [];
    
    // LCP Observer
    onLCP((metric) => {
      const lcpElement = metric.entries[metric.entries.length - 1]?.element;
      sendMetricUpdate('LCP', metric.value, lcpElement);
    });
    
    // CLS Observer
    onCLS((metric) => {
      sendMetricUpdate('CLS', metric.value);
      
      // Track layout shift sources
      metric.entries.forEach(entry => {
        entry.sources?.forEach(source => {
          if (source.node) {
            sendMetricUpdate('CLS_ELEMENT', entry.value, source.node);
          }
        });
      });
    });
    
    // INP Observer
    onINP((metric) => {
      const inpElement = metric.entries[metric.entries.length - 1]?.target;
      sendMetricUpdate('INP', metric.value, inpElement);
    });
    
    // Performance Observer for additional insights
    if ('PerformanceObserver' in window) {
      const perfObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach(entry => {
          performanceEntries.push({
            type: 'performance',
            name: entry.name,
            startTime: entry.startTime,
            duration: entry.duration,
            entryType: entry.entryType
          });
        });
      });
      
      try {
        perfObserver.observe({ 
          entryTypes: ['navigation', 'resource', 'paint', 'largest-contentful-paint'] 
        });
        observers.push(perfObserver);
      } catch (e) {
        console.warn('Some performance entry types not supported:', e);
      }
    }
  }
  
  // Listen for start collection message
  window.addEventListener('message', (event) => {
    if (event.source !== window) return;
    
    if (event.data.type === 'START_CWV_COLLECTION') {
      startCollection();
    }
    
    if (event.data.type === 'GET_PERFORMANCE_ENTRIES') {
      window.postMessage({
        type: 'PERFORMANCE_ENTRIES_RESPONSE',
        entries: performanceEntries
      }, '*');
    }
  });
  
  // Auto-start collection
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startCollection);
  } else {
    startCollection();
  }
})();
