class CoreWebVitalsDebugger {
  constructor() {
    this.metrics = {
      LCP: { value: null, element: null, rating: null },
      CLS: { value: null, elements: [], rating: null },
      INP: { value: null, element: null, rating: null }
    };
    
    this.overlay = null;
    this.highlightedElements = new Set();
    this.suggestions = null;
    this.isHighlightingEnabled = true;
    this.performanceEntries = [];
    
    // Add message listener
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleMessage(message, sender, sendResponse);
        return true; // Keep channel open for async response
      });
    }
    
    this.init();
  }

  async init() {
    await this.loadSuggestions();
    this.createOverlay();
    this.startMetricsCollection();
    this.setupEventListeners();
    this.injectWebVitalsScript();
  }

  async loadSuggestions() {
    try {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        const response = await fetch(chrome.runtime.getURL('suggestions.json'));
        this.suggestions = await response.json();
      } else {
        this.suggestions = this.getDefaultSuggestions();
      }
    } catch (error) {
      console.error('Failed to load suggestions:', error);
      this.suggestions = this.getDefaultSuggestions();
    }
  }

  getDefaultSuggestions() {
    return {
      LCP: {
        poor: [{ text: "Optimize images and use next-gen formats", link: "https://web.dev/lcp/" }],
        "needs-improvement": [{ text: "Reduce server response times", link: "https://web.dev/lcp/" }],
        good: [{ text: "Great LCP performance!", link: "https://web.dev/lcp/" }]
      },
      CLS: {
        poor: [{ text: "Set dimensions for images and ads", link: "https://web.dev/cls/" }],
        "needs-improvement": [{ text: "Avoid inserting content above existing content", link: "https://web.dev/cls/" }],
        good: [{ text: "Excellent layout stability!", link: "https://web.dev/cls/" }]
      },
      INP: {
        poor: [{ text: "Optimize JavaScript execution time", link: "https://web.dev/inp/" }],
        "needs-improvement": [{ text: "Reduce input delay", link: "https://web.dev/inp/" }],
        good: [{ text: "Great interaction responsiveness!", link: "https://web.dev/inp/" }]
      }
    };
  }

  injectWebVitalsScript() {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('injected.js');
      script.onload = () => script.remove();
      (document.head || document.documentElement).appendChild(script);
    } else {
      this.initInlineWebVitals();
    }
  }

  initInlineWebVitals() {
    // Fallback web vitals implementation
    const observer = new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (entry.entryType === 'largest-contentful-paint') {
          this.handleMetricUpdate('LCP', entry.startTime, entry.element);
        }
      }
    });
    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  }

  createOverlay() {
    this.overlay = document.createElement('div');
    this.overlay.className = 'cwv-overlay';
    this.overlay.innerHTML = `
      <div class="cwv-header">
        <span class="cwv-title">Core Web Vitals</span>
        <button class="cwv-toggle" title="Toggle highlighting">👁</button>
      </div>
      <div class="cwv-metrics">
        <div class="cwv-metric">
          <span class="cwv-metric-name">LCP</span>
          <span class="cwv-metric-value" id="cwv-lcp">-</span>
        </div>
        <div class="cwv-metric">
          <span class="cwv-metric-name">CLS</span>
          <span class="cwv-metric-value" id="cwv-cls">-</span>
        </div>
        <div class="cwv-metric">
          <span class="cwv-metric-name">INP</span>
          <span class="cwv-metric-value" id="cwv-inp">-</span>
        </div>
      </div>
      <button class="cwv-timeline-button" id="cwv-timeline">Open Timeline</button>
    `;
    
    this.injectStyles();
    document.body.appendChild(this.overlay);
    this.makeDraggable();
  }

  injectStyles() {
    if (!document.getElementById('cwv-styles')) {
      const style = document.createElement('style');
      style.id = 'cwv-styles';
      style.textContent = `
        .cwv-overlay {
          position: fixed;
          top: 20px;
          right: 20px;
          width: 280px;
          background: rgba(255, 255, 255, 0.95);
          border: 1px solid #ddd;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          z-index: 999999;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 12px;
          backdrop-filter: blur(10px);
        }
        .cwv-header {
          background: #1976d2;
          color: white;
          padding: 8px 12px;
          border-radius: 8px 8px 0 0;
          cursor: move;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .cwv-title {
          font-weight: 600;
        }
        .cwv-toggle {
          background: none;
          border: none;
          color: white;
          cursor: pointer;
          font-size: 14px;
        }
        .cwv-metrics {
          padding: 12px;
        }
        .cwv-metric {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
        }
        .cwv-metric-name {
          font-weight: 600;
        }
        .cwv-metric-value.good { color: #4caf50; }
        .cwv-metric-value.needs-improvement { color: #ff9800; }
        .cwv-metric-value.poor { color: #f44336; }
        .cwv-timeline-button {
          width: 100%;
          padding: 8px;
          background: #1976d2;
          color: white;
          border: none;
          border-radius: 0 0 8px 8px;
          cursor: pointer;
        }
        .cwv-element-highlight {
          outline: 3px solid #ff9800 !important;
          outline-offset: 2px !important;
        }
        .cwv-element-highlight.good {
          outline-color: #4caf50 !important;
        }
        .cwv-element-highlight.poor {
          outline-color: #f44336 !important;
        }
        .cwv-tooltip {
          position: absolute;
          background: rgba(0, 0, 0, 0.9);
          color: white;
          padding: 8px 12px;
          border-radius: 4px;
          font-size: 12px;
          max-width: 250px;
          z-index: 1000000;
        }
        .cwv-tooltip-title {
          font-weight: 600;
          margin-bottom: 4px;
        }
        .cwv-tooltip-suggestion {
          margin-bottom: 4px;
        }
        .cwv-tooltip-link {
          color: #64b5f6;
          text-decoration: none;
        }
        .cwv-overlay.dragging {
          opacity: 0.8;
        }
      `;
      document.head.appendChild(style);
    }
  }

  makeDraggable() {
    const header = this.overlay.querySelector('.cwv-header');
    let isDragging = false;
    let startX, startY, startLeft, startTop;

    header.addEventListener('mousedown', (e) => {
      isDragging = true;
      this.overlay.classList.add('dragging');
      startX = e.clientX;
      startY = e.clientY;
      const rect = this.overlay.getBoundingClientRect();
      startLeft = rect.left;
      startTop = rect.top;
      e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;
      
      const newLeft = startLeft + (e.clientX - startX);
      const newTop = startTop + (e.clientY - startY);
      
      this.overlay.style.left = `${Math.max(0, Math.min(window.innerWidth - 280, newLeft))}px`;
      this.overlay.style.top = `${Math.max(0, Math.min(window.innerHeight - 200, newTop))}px`;
      this.overlay.style.right = 'auto';
    });

    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false;
        this.overlay.classList.remove('dragging');
      }
    });
  }

  setupEventListeners() {
    // Toggle highlighting
    this.overlay.querySelector('.cwv-toggle').addEventListener('click', () => {
      this.isHighlightingEnabled = !this.isHighlightingEnabled;
      if (!this.isHighlightingEnabled) {
        this.clearHighlights();
      } else {
        this.updateHighlights();
      }
    });

    // Timeline button
    this.overlay.querySelector('#cwv-timeline').addEventListener('click', () => {
      this.openDevToolsPanel();
    });

    // Listen for metric updates from injected script
    window.addEventListener('message', (event) => {
      if (event.source !== window) return;
      
      if (event.data.type === 'CWV_METRIC_UPDATE') {
        this.handleMetricUpdate(event.data.metric, event.data.value, event.data.element);
      }
    });

    // Handle SPA navigation
    let lastUrl = location.href;
    new MutationObserver(() => {
      if (location.href !== lastUrl) {
        lastUrl = location.href;
        this.resetMetrics();
        setTimeout(() => this.startMetricsCollection(), 100);
      }
    }).observe(document, { subtree: true, childList: true });
  }

  startMetricsCollection() {
    window.postMessage({ type: 'START_CWV_COLLECTION' }, '*');
  }

  handleMetricUpdate(metricName, value, elementSelector) {
    this.metrics[metricName].value = value;
    this.metrics[metricName].rating = this.getRating(metricName, value);
    
    if (elementSelector) {
      const element = typeof elementSelector === 'string' 
        ? document.querySelector(elementSelector) 
        : elementSelector;
      if (element) {
        this.metrics[metricName].element = element;
      }
    }

    this.updateOverlay();
    if (this.isHighlightingEnabled) {
      this.updateHighlights();
    }
  }

  getRating(metric, value) {
    const thresholds = {
      LCP: { good: 2500, poor: 4000 },
      CLS: { good: 0.1, poor: 0.25 },
      INP: { good: 200, poor: 500 }
    };

    const threshold = thresholds[metric];
    if (!threshold) return 'good';
    
    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }

  updateOverlay() {
    Object.keys(this.metrics).forEach(metric => {
      const element = document.getElementById(`cwv-${metric.toLowerCase()}`);
      const data = this.metrics[metric];
      
      if (element && data.value !== null) {
        const displayValue = metric === 'CLS' 
          ? data.value.toFixed(3) 
          : `${Math.round(data.value)}ms`;
        
        element.textContent = displayValue;
        element.className = `cwv-metric-value ${data.rating}`;
      }
    });
  }

  updateHighlights() {
    this.clearHighlights();
    
    Object.keys(this.metrics).forEach(metric => {
      const data = this.metrics[metric];
      if (data.element && data.rating) {
        this.highlightElement(data.element, data.rating, metric);
      }
    });
  }

  highlightElement(element, rating, metric) {
    if (!element || this.highlightedElements.has(element)) return;
    
    element.classList.add('cwv-element-highlight', rating);
    this.highlightedElements.add(element);
    
    this.addTooltipListeners(element, metric, rating);
    
    setTimeout(() => {
      if (this.highlightedElements.has(element)) {
        this.removeHighlight(element);
      }
    }, 5000);
  }

  addTooltipListeners(element, metric, rating) {
    let tooltip = null;
    
    const showTooltip = (e) => {
      tooltip = this.createTooltip(metric, rating, e.pageX, e.pageY);
      document.body.appendChild(tooltip);
    };
    
    const hideTooltip = () => {
      if (tooltip) {
        tooltip.remove();
        tooltip = null;
      }
    };
    
    const moveTooltip = (e) => {
      if (tooltip) {
        tooltip.style.left = `${e.pageX + 10}px`;
        tooltip.style.top = `${e.pageY + 10}px`;
      }
    };
    
    element.addEventListener('mouseenter', showTooltip);
    element.addEventListener('mouseleave', hideTooltip);
    element.addEventListener('mousemove', moveTooltip);
  }

  createTooltip(metric, rating, x, y) {
    const tooltip = document.createElement('div');
    tooltip.className = 'cwv-tooltip';
    
    const suggestions = this.getSuggestions(metric, rating);
    
    tooltip.innerHTML = `
      <div class="cwv-tooltip-title">${metric} Optimization</div>
      ${suggestions.map(s => `<div class="cwv-tooltip-suggestion">${s.text}</div>`).join('')}
      <a href="${suggestions[0]?.link || '#'}" target="_blank" class="cwv-tooltip-link">Learn more →</a>
    `;
    
    tooltip.style.left = `${x + 10}px`;
    tooltip.style.top = `${y + 10}px`;
    
    return tooltip;
  }

  getSuggestions(metric, rating) {
    return this.suggestions[metric]?.[rating] || [{
      text: `Improve ${metric} performance`,
      link: 'https://web.dev/vitals/'
    }];
  }

  removeHighlight(element) {
    element.classList.remove('cwv-element-highlight', 'good', 'needs-improvement', 'poor');
    this.highlightedElements.delete(element);
  }

  clearHighlights() {
    this.highlightedElements.forEach(element => {
      this.removeHighlight(element);
    });
  }

  resetMetrics() {
    this.metrics = {
      LCP: { value: null, element: null, rating: null },
      CLS: { value: null, elements: [], rating: null },
      INP: { value: null, element: null, rating: null }
    };
    this.clearHighlights();
    this.updateOverlay();
  }

  openDevToolsPanel() {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({ action: 'openDevTools' });
    } else {
      console.log('DevTools panel opening requires Chrome extension context');
    }
  }

  handleMessage(message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'ping':
          sendResponse({ pong: true });
          break;
          
        case 'toggleHighlighting':
          this.isHighlightingEnabled = message.data;
          if (!this.isHighlightingEnabled) {
            this.clearHighlights();
          } else {
            this.updateHighlights();
          }
          sendResponse({ success: true });
          break;
          
        case 'toggleOverlay':
          this.overlay.style.display = message.data ? 'block' : 'none';
          sendResponse({ success: true });
          break;
          
        case 'exportData':
          this.exportMetricsData();
          sendResponse({ success: true });
          break;
          
        case 'resetMetrics':
          this.resetMetrics();
          sendResponse({ success: true });
          break;
          
        case 'startDevToolsCollection':
          this.startDevToolsDataSharing();
          sendResponse({ success: true });
          break;
          
        case 'highlightEventElement':
          this.highlightElementFromEvent(message.event);
          sendResponse({ success: true });
          break;

        case 'getMetrics':
          sendResponse({ metrics: this.metrics });
          break;

        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message });
    }
  }

  startDevToolsDataSharing() {
    if (this.dataShareInterval) {
      clearInterval(this.dataShareInterval);
    }

    const shareData = () => {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        try {
          chrome.runtime.sendMessage({
            type: 'devtools-data',
            metrics: this.metrics,
            performanceEntries: this.performanceEntries,
            timestamp: Date.now()
          });
        } catch (error) {
          console.error('Error sharing data with DevTools:', error);
        }
      }
    };
    
    this.dataShareInterval = setInterval(shareData, 1000);
    shareData();
  }

  exportMetricsData() {
    const data = {
      url: window.location.href,
      timestamp: Date.now(),
      metrics: this.metrics,
      performanceEntries: this.performanceEntries.slice(-100), // Last 100 entries
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : null
    };
    
    try {
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `cwv-data-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  }

  highlightElementFromEvent(event) {
    if (!event || !event.elementSelector) return;

    try {
      const element = document.querySelector(event.elementSelector);
      if (element) {
        this.highlightElement(element, 'needs-improvement', event.type || 'event');
        
        element.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'center',
          inline: 'nearest'
        });

        // Flash effect
        let flashCount = 0;
        const flashInterval = setInterval(() => {
          element.style.backgroundColor = flashCount % 2 === 0 ? '#ffeb3b' : '';
          flashCount++;
          if (flashCount > 6) {
            clearInterval(flashInterval);
            element.style.backgroundColor = '';
          }
        }, 200);
      }
    } catch (error) {
      console.error('Error highlighting element from event:', error);
    }
  }

  collectPerformanceEntries() {
    try {
      const entries = performance.getEntriesByType('navigation')
        .concat(performance.getEntriesByType('resource'))
        .concat(performance.getEntriesByType('paint'))
        .concat(performance.getEntriesByType('largest-contentful-paint'))
        .concat(performance.getEntriesByType('layout-shift'));

      this.performanceEntries = entries.map(entry => ({
        name: entry.name,
        entryType: entry.entryType,
        startTime: entry.startTime,
        duration: entry.duration,
        transferSize: entry.transferSize || 0,
        value: entry.value || 0
      }));
    } catch (error) {
      console.error('Error collecting performance entries:', error);
    }
  }

  destroy() {
    if (this.overlay && this.overlay.parentNode) {
      this.overlay.parentNode.removeChild(this.overlay);
    }
    
    if (this.dataShareInterval) {
      clearInterval(this.dataShareInterval);
    }
    
    this.clearHighlights();
    
    const styles = document.getElementById('cwv-styles');
    if (styles) {
      styles.remove();
    }
  }
}

// Enhanced initialization with error handling
(function initializeCWVDebugger() {
  let debugger = null;

  const init = () => {
    try {
      if (debugger) {
        debugger.destroy();
      }
      debugger = new CoreWebVitalsDebugger();
      
      // Expose to global scope for debugging
      window.cwvDebugger = debugger;
      
    } catch (error) {
      console.error('Failed to initialize Core Web Vitals Debugger:', error);
    }
  };

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  // Handle page unload
  window.addEventListener('beforeunload', () => {
    if (debugger) {
      debugger.destroy();
    }
  });

  // Re-initialize on navigation for SPAs
  let lastUrl = location.href;
  const observer = new MutationObserver(() => {
    if (location.href !== lastUrl) {
      lastUrl = location.href;
      setTimeout(init, 500);
    }
  });
  
  observer.observe(document, { 
    subtree: true, 
    childList: true 
  });
})();
