// panel.js
class CWVTimelinePanel {
  constructor() {
    this.canvas = document.getElementById('timeline-canvas');
    this.ctx = this.canvas.getContext('2d');
    this.events = [];
    this.metrics = { LCP: null, CLS: null, INP: null };
    this.autoRefresh = true;
    this.startTime = performance.now();
    
    this.init();
  }
  
  init() {
    this.setupCanvas();
    this.setupEventListeners();
    this.startDataCollection();
    this.startRenderLoop();
  }
  
  setupCanvas() {
    const rect = this.canvas.getBoundingClientRect();
    this.canvas.width = rect.width * devicePixelRatio;
    this.canvas.height = rect.height * devicePixelRatio;
    this.ctx.scale(devicePixelRatio, devicePixelRatio);
    this.canvas.style.width = rect.width + 'px';
    this.canvas.style.height = rect.height + 'px';
  }
  
  setupEventListeners() {
    document.getElementById('auto-refresh').addEventListener('click', (e) => {
      this.autoRefresh = !this.autoRefresh;
      e.target.classList.toggle('active', this.autoRefresh);
    });
    
    document.getElementById('clear-timeline').addEventListener('click', () => {
      this.events = [];
      this.clearEventsContainer();
      this.render();
    });
    
    document.getElementById('export-timeline').addEventListener('click', () => {
      this.exportTimelineData();
    });
    
    this.canvas.addEventListener('click', (e) => {
      this.handleCanvasClick(e);
    });
    
    window.addEventListener('resize', () => {
      this.setupCanvas();
      this.render();
    });
  }
  
  startDataCollection() {
    // Connect to content script to receive performance data
    const port = chrome.runtime.connect({ name: 'devtools-panel' });
    
    port.postMessage({ action: 'startCollection' });
    
    port.onMessage.addListener((message) => {
      if (message.type === 'metricUpdate') {
        this.handleMetricUpdate(message.metric, message.value, message.timestamp);
      } else if (message.type === 'performanceEvent') {
        this.addEvent(message.event);
      }
    });
    
    // Request initial data
    setInterval(() => {
      if (this.autoRefresh) {
        port.postMessage({ action: 'getLatestData' });
      }
    }, 1000);
  }
  
  handleMetricUpdate(metricName, value, timestamp) {
    this.metrics[metricName] = { value, timestamp };
    this.updateMetricDisplay(metricName, value);
    
    this.addEvent({
      type: metricName,
      value: value,
      timestamp: timestamp,
      description: `${metricName} updated to ${this.formatValue(metricName, value)}`
    });
  }
  
  updateMetricDisplay(metricName, value) {
    const element = document.getElementById(`${metricName.toLowerCase()}-value`);
    if (element) {
      const displayValue = this.formatValue(metricName, value);
      const rating = this.getRating(metricName, value);
      
      element.textContent = displayValue;
      element.className = `metric-value ${rating}`;
    }
  }
  
  formatValue(metricName, value) {
    return metricName === 'CLS' ? value.toFixed(3) : `${Math.round(value)}ms`;
  }
  
  getRating(metric, value) {
    const thresholds = {
      LCP: { good: 2500, poor: 4000 },
      CLS: { good: 0.1, poor: 0.25 },
      INP: { good: 200, poor: 500 }
    };
    
    const threshold = thresholds[metric];
    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }
  
  addEvent(event) {
    this.events.push({
      ...event,
      id: Date.now() + Math.random()
    });
    
    // Keep only last 100 events
    if (this.events.length > 100) {
      this.events = this.events.slice(-100);
    }
    
    this.updateEventsContainer();
    this.render();
  }
  
  updateEventsContainer() {
    const container = document.getElementById('events-container');
    const sortedEvents = [...this.events].sort((a, b) => b.timestamp - a.timestamp);
    
    container.innerHTML = sortedEvents.map(event => `
      <div class="event-item" data-event-id="${event.id}">
        <div class="event-time">${Math.round(event.timestamp - this.startTime)}ms</div>
        <div class="event-type ${event.type.toLowerCase()}">${event.type}</div>
        <div class="event-description">${event.description}</div>
        <div class="event-value">${event.value ? this.formatValue(event.type, event.value) : ''}</div>
      </div>
    `).join('');
    
    // Add click handlers
    container.querySelectorAll('.event-item').forEach(item => {
      item.addEventListener('click', () => {
        const eventId = item.dataset.eventId;
        this.highlightEvent(eventId);
      });
    });
  }
  
  clearEventsContainer() {
    document.getElementById('events-container').innerHTML = '';
  }
  
  highlightEvent(eventId) {
    const event = this.events.find(e => e.id == eventId);
    if (event) {
      // Send message to content script to highlight related element
      chrome.runtime.sendMessage({
        action: 'highlightEventElement',
        event: event
      });
    }
  }
  
  render() {
    const ctx = this.ctx;
    const canvas = this.canvas;
    const width = canvas.width / devicePixelRatio;
    const height = canvas.height / devicePixelRatio;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    if (this.events.length === 0) {
      ctx.fillStyle = '#999';
      ctx.font = '14px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('No performance data yet...', width / 2, height / 2);
      return;
    }
    
    const now = performance.now();
    const timeRange = 10000; // 10 seconds
    const startTime = now - timeRange;
    
    // Draw timeline background
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, width, height);
    
    // Draw time grid
    this.drawTimeGrid(ctx, width, height, startTime, timeRange);
    
    // Draw performance events
    this.drawEvents(ctx, width, height, startTime, timeRange);
    
    // Draw metric markers
    this.drawMetricMarkers(ctx, width, height, startTime, timeRange);
  }
  
  drawTimeGrid(ctx, width, height, startTime, timeRange) {
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 1;
    ctx.font = '10px Arial';
    ctx.fillStyle = '#666';
    
    const gridInterval = 1000; // 1 second intervals
    const steps = Math.ceil(timeRange / gridInterval);
    
    for (let i = 0; i <= steps; i++) {
      const x = (i / steps) * width;
      const time = startTime + (i * gridInterval);
      
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
      
      ctx.fillText(`${Math.round((time - this.startTime) / 1000)}s`, x + 2, 12);
    }
  }
  
  drawEvents(ctx, width, height, startTime, timeRange) {
    const eventHeight = 20;
    const eventTypes = ['LCP', 'CLS', 'INP', 'paint', 'resource'];
    const colors = {
      LCP: '#1976d2',
      CLS: '#f57c00',
      INP: '#7b1fa2',
      paint: '#388e3c',
      resource: '#666'
    };
    
    this.events.forEach((event, index) => {
      if (event.timestamp < startTime || event.timestamp > startTime + timeRange) return;
      
      const x = ((event.timestamp - startTime) / timeRange) * width;
      const typeIndex = eventTypes.indexOf(event.type) || 0;
      const y = 30 + (typeIndex * eventHeight);
      
      ctx.fillStyle = colors[event.type] || '#666';
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();
      
      // Draw event line
      ctx.strokeStyle = colors[event.type] || '#666';
      ctx.lineWidth = 1;
      ctx.setLineDash([2, 2]);
      ctx.beginPath();
      ctx.moveTo(x, y + 4);
      ctx.lineTo(x, height - 10);
      ctx.stroke();
      ctx.setLineDash([]);
    });
    
    // Draw event type labels
    ctx.fillStyle = '#333';
    ctx.font = '11px Arial';
    eventTypes.forEach((type, index) => {
      const y = 35 + (index * eventHeight);
      ctx.fillText(type, 5, y);
    });
  }
  
  drawMetricMarkers(ctx, width, height, startTime, timeRange) {
    const markerColors = {
      LCP: '#1976d2',
      CLS: '#f57c00',
      INP: '#7b1fa2'
    };
    
    Object.keys(this.metrics).forEach(metricName => {
      const metric = this.metrics[metricName];
      if (!metric || metric.timestamp < startTime || metric.timestamp > startTime + timeRange) return;
      
      const x = ((metric.timestamp - startTime) / timeRange) * width;
      
      ctx.strokeStyle = markerColors[metricName];
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
      
      // Draw metric value
      ctx.fillStyle = markerColors[metricName];
      ctx.font = 'bold 12px Arial';
      const text = `${metricName}: ${this.formatValue(metricName, metric.value)}`;
      ctx.fillText(text, x + 5, 20);
    });
  }
  
  handleCanvasClick(e) {
    const rect = this.canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const width = rect.width;
    
    const now = performance.now();
    const timeRange = 10000;
    const startTime = now - timeRange;
    const clickTime = startTime + (x / width) * timeRange;
    
    // Find closest event to click
    const closestEvent = this.events.reduce((closest, event) => {
      const distance = Math.abs(event.timestamp - clickTime);
      return distance < Math.abs(closest.timestamp - clickTime) ? event : closest;
    }, this.events[0]);
    
    if (closestEvent && Math.abs(closestEvent.timestamp - clickTime) < 500) {
      this.highlightEvent(closestEvent.id);
    }
  }
  
  startRenderLoop() {
    const render = () => {
      if (this.autoRefresh) {
        this.render();
      }
      requestAnimationFrame(render);
    };
    render();
  }
  
  exportTimelineData() {
    const data = {
      metrics: this.metrics,
      events: this.events,
      timestamp: Date.now(),
      url: window.location.href
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cwv-timeline-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }
}

// Initialize panel when shown
window.initPanel = () => {
  new CWVTimelinePanel();
};
