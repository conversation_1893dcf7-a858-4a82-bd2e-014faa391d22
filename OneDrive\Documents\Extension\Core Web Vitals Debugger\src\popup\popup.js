document.addEventListener('DOMContentLoaded', async () => {
  const highlightingToggle = document.getElementById('highlighting-toggle');
  const overlayToggle = document.getElementById('overlay-toggle');
  const openDevToolsBtn = document.getElementById('open-devtools');
  const exportDataBtn = document.getElementById('export-data');
  const resetMetricsBtn = document.getElementById('reset-metrics');
  const statusEl = document.getElementById('status');
  
  // Load saved settings
  const settings = await chrome.storage.sync.get({
    highlightingEnabled: true,
    overlayEnabled: true
  });
  
  highlightingToggle.checked = settings.highlightingEnabled;
  overlayToggle.checked = settings.overlayEnabled;
  
  // Save settings on change
  highlightingToggle.addEventListener('change', async () => {
    await chrome.storage.sync.set({ highlightingEnabled: highlightingToggle.checked });
    sendMessageToContent('toggleHighlighting', highlightingToggle.checked);
  });
  
  overlayToggle.addEventListener('change', async () => {
    await chrome.storage.sync.set({ overlayEnabled: overlayToggle.checked });
    sendMessageToContent('toggleOverlay', overlayToggle.checked);
  });
  
  // Button handlers
  openDevToolsBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ action: 'openDevTools' });
  });
  
  exportDataBtn.addEventListener('click', () => {
    sendMessageToContent('exportData');
  });
  
  resetMetricsBtn.addEventListener('click', () => {
    sendMessageToContent('resetMetrics');
    statusEl.textContent = 'Metrics reset successfully';
    setTimeout(() => {
      statusEl.textContent = 'Extension active on current tab';
    }, 2000);
  });

  async function sendMessageToContent(action, data = null) {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      await chrome.tabs.sendMessage(tab.id, { action, data });
    } catch (error) {
      console.error('Failed to send message to content script:', error);
      statusEl.textContent = 'Error: Please refresh the page';
    }
  }
  
  // Check if extension is working on current tab
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const response = await chrome.tabs.sendMessage(tab.id, { action: 'ping' });
    if (response?.pong) {
      statusEl.textContent = 'Extension active on current tab';
    }
  } catch (error) {
    statusEl.textContent = 'Please refresh the page to activate';
  }
});