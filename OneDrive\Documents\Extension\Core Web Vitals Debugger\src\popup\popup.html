<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 16px;
    }
    
    .logo {
      font-size: 24px;
      margin-bottom: 8px;
    }
    
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    
    .subtitle {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
    
    .controls {
      margin: 16px 0;
    }
    
    .control-group {
      margin-bottom: 12px;
    }
    
    .control-label {
      font-size: 12px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      display: block;
    }
    
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }
    
    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }
    
    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    
    input:checked + .toggle-slider {
      background-color: #4CAF50;
    }
    
    input:checked + .toggle-slider:before {
      transform: translateX(26px);
    }
    
    .button {
      width: 100%;
      padding: 8px 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      margin-bottom: 8px;
      transition: opacity 0.2s;
    }
    
    .button:hover {
      opacity: 0.9;
    }
    
    .status {
      font-size: 11px;
      color: #666;
      text-align: center;
      margin-top: 12px;
    }
    
    .links {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid #eee;
    }
    
    .link {
      display: block;
      color: #4fc3f7;
      text-decoration: none;
      font-size: 11px;
      margin-bottom: 4px;
    }
    
    .link:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">⚡</div>
    <div class="title">CWV Debugger</div>
    <div class="subtitle">Real-time Core Web Vitals monitoring</div>
  </div>
  
  <div class="controls">
    <div class="control-group">
      <label class="control-label">Auto-highlighting</label>
      <label class="toggle-switch">
        <input type="checkbox" id="highlighting-toggle" checked>
        <span class="toggle-slider"></span>
      </label>
    </div>
    
    <div class="control-group">
      <label class="control-label">Show overlay</label>
      <label class="toggle-switch">
        <input type="checkbox" id="overlay-toggle" checked>
        <span class="toggle-slider"></span>
      </label>
    </div>
  </div>
  
  <button class="button" id="open-devtools">Open Performance Timeline</button>
  <button class="button" id="export-data">Export Metrics Data</button>
  <button class="button" id="reset-metrics">Reset All Metrics</button>
  
  <div class="status" id="status">
    Extension active on current tab
  </div>
  
  <div class="links">
    <a href="https://web.dev/vitals/" target="_blank" class="link">Learn about Core Web Vitals</a>
    <a href="https://github.com/GoogleChrome/web-vitals" target="_blank" class="link">Web-vitals library docs</a>
    <a href="#" id="help-link" class="link">Extension help & settings</a>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
