<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      margin: 0;
      padding: 16px;
      background: #f5f5f5;
    }
    
    .timeline-container {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .timeline-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .timeline-title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    
    .timeline-controls {
      display: flex;
      gap: 8px;
    }
    
    .control-button {
      padding: 6px 12px;
      border: 1px solid #ddd;
      background: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }
    
    .control-button:hover {
      background: #f0f0f0;
    }
    
    .control-button.active {
      background: #4CAF50;
      color: white;
      border-color: #4CAF50;
    }
    
    .timeline-canvas {
      width: 100%;
      height: 300px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #fafafa;
    }
    
    .metrics-summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 20px;
    }
    
    .metric-card {
      background: white;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .metric-name {
      font-size: 12px;
      font-weight: bold;
      color: #666;
      text-transform: uppercase;
      margin-bottom: 8px;
    }
    
    .metric-value {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 4px;
    }
    
    .metric-value.good { color: #0cce6b; }
    .metric-value.needs-improvement { color: #ffa400; }
    .metric-value.poor { color: #ff4e42; }
    
    .metric-description {
      font-size: 11px;
      color: #999;
    }
    
    .events-list {
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .events-header {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #333;
    }
    
    .event-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #eee;
      cursor: pointer;
    }
    
    .event-item:hover {
      background: #f9f9f9;
    }
    
    .event-time {
      width: 80px;
      font-size: 11px;
      color: #666;
      font-family: monospace;
    }
    
    .event-type {
      width: 60px;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 3px;
      text-align: center;
      margin-right: 12px;
    }
    
    .event-type.lcp { background: #e3f2fd; color: #1976d2; }
    .event-type.cls { background: #fff3e0; color: #f57c00; }
    .event-type.inp { background: #f3e5f5; color: #7b1fa2; }
    .event-type.paint { background: #e8f5e8; color: #388e3c; }
    
    .event-description {
      flex: 1;
      font-size: 12px;
      color: #333;
    }
    
    .event-value {
      font-size: 11px;
      color: #666;
      font-family: monospace;
    }
    
    .filmstrip {
      display: flex;
      gap: 8px;
      margin-top: 16px;
      overflow-x: auto;
      padding: 8px 0;
    }
    
    .filmstrip-frame {
      width: 120px;
      height: 80px;
      border: 2px solid #ddd;
      border-radius: 4px;
      background: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      color: #666;
      cursor: pointer;
      flex-shrink: 0;
    }
    
    .filmstrip-frame.active {
      border-color: #4CAF50;
    }
    
    .filmstrip-frame:hover {
      border-color: #2196F3;
    }
  </style>
</head>
<body>
  <div class="metrics-summary">
    <div class="metric-card">
      <div class="metric-name">Largest Contentful Paint</div>
      <div class="metric-value" id="lcp-value">-</div>
      <div class="metric-description">Time to render largest element</div>
    </div>
    <div class="metric-card">
      <div class="metric-name">Cumulative Layout Shift</div>
      <div class="metric-value" id="cls-value">-</div>
      <div class="metric-description">Visual stability score</div>
    </div>
    <div class="metric-card">
      <div class="metric-name">Interaction to Next Paint</div>
      <div class="metric-value" id="inp-value">-</div>
      <div class="metric-description">Interaction responsiveness</div>
    </div>
  </div>
  
  <div class="timeline-container">
    <div class="timeline-header">
      <div class="timeline-title">Performance Timeline</div>
      <div class="timeline-controls">
        <button class="control-button active" id="auto-refresh">Auto Refresh</button>
        <button class="control-button" id="clear-timeline">Clear</button>
        <button class="control-button" id="export-timeline">Export</button>
      </div>
    </div>
    <canvas class="timeline-canvas" id="timeline-canvas"></canvas>
    
    <div class="filmstrip" id="filmstrip">
      <!-- Filmstrip frames will be generated here -->
    </div>
  </div>
  
  <div class="events-list">
    <div class="events-header">Performance Events</div>
    <div id="events-container">
      <div class="event-item">
        <div class="event-time">0ms</div>
        <div class="event-type paint">PAINT</div>
        <div class="event-description">Navigation start</div>
        <div class="event-value">-</div>
      </div>
    </div>
  </div>
  
  <script src="panel.js"></script>
</body>
</html>
