(function() {
  'use strict';
  
  window.webVitals = {
    onLCP: function(callback) {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          if (lastEntry) {
            callback({
              value: lastEntry.startTime,
              entries: entries
            });
          }
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      }
    },
    
    onCLS: function(callback) {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          let clsValue = 0;
          const entries = list.getEntries();
          entries.forEach(entry => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          callback({
            value: clsValue,
            entries: entries
          });
        });
        observer.observe({ entryTypes: ['layout-shift'] });
      }
    },
    
    onINP: function(callback) {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            if (entry.processingStart && entry.startTime) {
              const inp = entry.processingStart - entry.startTime;
              callback({
                value: inp,
                entries: [entry]
              });
            }
          });
        });
        observer.observe({ entryTypes: ['event'] });
      }
    }
  };
})();
