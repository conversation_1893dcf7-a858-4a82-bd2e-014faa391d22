chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'openDevTools') {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, { action: 'openDevTools' });
      }
    });
  }
});

// Handle DevTools connection
chrome.runtime.onConnect.addListener((port) => {
  if (port.name === 'devtools-panel') {
    port.onMessage.addListener((message) => {
      if (message.action === 'startCollection') {
        // Forward to content script
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, { 
              action: 'startDevToolsCollection',
              port: port.name 
            });
          }
        });
      }
    });
  }
});

// Performance impact monitoring
chrome.runtime.onStartup.addListener(() => {
  console.log('CWV Debugger extension started');
});
