/* content.css */
.cwv-overlay {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 280px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 12px;
  z-index: 999999;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: opacity 0.3s ease;
}

.cwv-overlay.dragging {
  user-select: none;
}

.cwv-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  cursor: move;
}

.cwv-title {
  font-weight: bold;
  font-size: 14px;
}

.cwv-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
}

.cwv-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 0;
}

.cwv-metric-name {
  font-weight: 500;
}

.cwv-metric-value {
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: bold;
  min-width: 60px;
  text-align: center;
}

.cwv-metric-value.good {
  background: #0cce6b;
  color: white;
}

.cwv-metric-value.needs-improvement {
  background: #ffa400;
  color: white;
}

.cwv-metric-value.poor {
  background: #ff4e42;
  color: white;
}

.cwv-element-highlight {
  outline: 3px solid !important;
  position: relative !important;
  z-index: 999998 !important;
  animation: cwv-pulse 2s infinite;
}

.cwv-element-highlight.good {
  outline-color: #0cce6b !important;
}

.cwv-element-highlight.needs-improvement {
  outline-color: #ffa400 !important;
}

.cwv-element-highlight.poor {
  outline-color: #ff4e42 !important;
}

@keyframes cwv-pulse {
  0% { outline-width: 3px; }
  50% { outline-width: 5px; }
  100% { outline-width: 3px; }
}

.cwv-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.95);
  color: white;
  padding: 12px;
  border-radius: 6px;
  font-size: 12px;
  max-width: 300px;
  z-index: 1000000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.cwv-tooltip-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #ffa400;
}

.cwv-tooltip-suggestion {
  margin-bottom: 6px;
  line-height: 1.4;
}

.cwv-tooltip-link {
  color: #4fc3f7;
  text-decoration: none;
  font-size: 11px;
}

.cwv-timeline-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  margin-top: 8px;
  width: 100%;
}

.cwv-timeline-button:hover {
  opacity: 0.9;
}
