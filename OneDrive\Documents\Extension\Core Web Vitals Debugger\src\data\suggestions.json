// suggestions.json
{
  "LCP": {
    "poor": [
      {
        "text": "Optimize images: Use WebP format, implement lazy loading, and add proper sizing attributes",
        "link": "https://web.dev/optimize-lcp/"
      },
      {
        "text": "Eliminate render-blocking resources: Defer non-critical CSS and JavaScript",
        "link": "https://web.dev/render-blocking-resources/"
      },
      {
        "text": "Improve server response times: Use CDN, optimize database queries, and implement caching",
        "link": "https://web.dev/time-to-first-byte/"
      }
    ],
    "needs-improvement": [
      {
        "text": "Preload important resources: Use <link rel='preload'> for critical images and fonts",
        "link": "https://web.dev/preload-critical-assets/"
      },
      {
        "text": "Optimize web fonts: Use font-display: swap and preload font files",
        "link": "https://web.dev/font-display/"
      }
    ],
    "good": [
      {
        "text": "Great LCP performance! Consider monitoring for regressions",
        "link": "https://web.dev/lcp/"
      }
    ]
  },
  "CLS": {
    "poor": [
      {
        "text": "Set explicit dimensions for images and video elements",
        "link": "https://web.dev/optimize-cls/"
      },
      {
        "text": "Reserve space for ads and embeds using aspect-ratio CSS property",
        "link": "https://web.dev/cls/"
      },
      {
        "text": "Avoid inserting content above existing content, especially near viewport",
        "link": "https://web.dev/cls/"
      }
    ],
    "needs-improvement": [
      {
        "text": "Use CSS transform instead of changing layout properties",
        "link": "https://web.dev/avoid-large-complex-layouts-and-layout-thrashing/"
      },
      {
        "text": "Preload fonts to prevent FOIT/FOUT layout shifts",
        "link": "https://web.dev/preload-optional-fonts/"
      }
    ],
    "good": [
      {
        "text": "Excellent layout stability! Keep monitoring for dynamic content changes",
        "link": "https://web.dev/cls/"
      }
    ]
  },
  "INP": {
    "poor": [
      {
        "text": "Break up long JavaScript tasks using setTimeout or requestIdleCallback",
        "link": "https://web.dev/optimize-inp/"
      },
      {
        "text": "Debounce expensive operations and use passive event listeners",
        "link": "https://web.dev/inp/"
      },
      {
        "text": "Optimize third-party scripts and consider web workers for heavy computations",
        "link": "https://web.dev/third-party-summary/"
      }
    ],
    "needs-improvement": [
      {
        "text": "Reduce DOM complexity and optimize CSS selectors",
        "link": "https://web.dev/dom-size/"
      },
      {
        "text": "Use CSS containment to limit layout recalculation scope",
        "link": "https://web.dev/css-containment/"
      }
    ],
    "good": [
      {
        "text": "Great interaction responsiveness! Monitor for performance regressions",
        "link": "https://web.dev/inp/"
      }
    ]
  }
}
