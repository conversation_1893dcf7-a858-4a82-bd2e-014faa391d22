{"name": "seo-core-web-vitals-debugger", "version": "1.0.0", "description": "Real-time Core Web Vitals monitoring and debugging extension", "scripts": {"build": "npm run copy-web-vitals && npm run validate", "copy-web-vitals": "cp node_modules/web-vitals/dist/web-vitals.js libs/", "validate": "web-ext lint", "test": "jest", "dev": "web-ext run --start-url https://web.dev"}, "dependencies": {"web-vitals": "^3.5.0"}, "devDependencies": {"web-ext": "^7.8.0", "jest": "^29.7.0"}}